<template>
  <div class="digital-human-wrapper">
    <!-- 数字人视频容器 -->
    <div class="digital-human-container" :style="containerStyle">
      <video
        ref="digitalHumanVideo"
        class="digital-human-video"
        autoplay
        playsinline
        :muted="false"
      ></video>
      <audio
        ref="digitalHumanAudio"
        autoplay
      ></audio>
      
      <!-- 连接状态指示器 -->
      <div class="status-indicator" :class="statusClass">
        <span class="status-dot"></span>
        <span class="status-text">{{ statusText }}</span>
      </div>
      
      <!-- 加载遮罩 -->
      <div v-if="isConnecting" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p>正在连接数字人...</p>
      </div>
    </div>
    
    <!-- 控制面板 -->
    <div class="controls-panel" v-if="showControls">
      <div class="control-buttons">
        <button
          v-if="!isConnected"
          @click="connect"
          :disabled="isConnecting"
          class="btn btn-primary"
        >
          {{ isConnecting ? '连接中...' : '连接数字人' }}
        </button>
        
        <button
          v-if="isConnected"
          @click="disconnect"
          class="btn btn-danger"
        >
          断开连接
        </button>
      </div>
      
      <!-- 消息输入 -->
      <div class="message-input-group" v-if="isConnected">
        <div class="input-wrapper">
          <textarea
            v-model="messageText"
            @keydown.enter.prevent="sendMessage"
            placeholder="输入要对数字人说的话..."
            class="message-input"
            rows="2"
          ></textarea>
          <button
            @click="sendMessage"
            :disabled="!messageText.trim()"
            class="btn btn-send"
          >
            发送
          </button>
        </div>
        
        <!-- 模式切换 -->
        <div class="mode-switch">
          <label class="radio-label">
            <input
              type="radio"
              value="chat"
              v-model="messageType"
            />
            对话模式
          </label>
          <label class="radio-label">
            <input
              type="radio"
              value="echo"
              v-model="messageType"
            />
            朗读模式
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DigitalHuman',
  props: {
    // 服务器地址
    serverUrl: {
      type: String,
      default: 'http://************:8010'
    },
    // 是否显示控制面板
    showControls: {
      type: Boolean,
      default: true
    },
    // 容器宽度
    width: {
      type: [String, Number],
      default: 400
    },
    // 容器高度
    height: {
      type: [String, Number],
      default: 300
    },
    // 是否自动连接
    autoConnect: {
      type: Boolean,
      default: false
    },
    // 默认消息类型
    defaultMessageType: {
      type: String,
      default: 'chat',
      validator: value => ['chat', 'echo'].includes(value)
    }
  },
  data() {
    return {
      pc: null,
      sessionId: 0,
      isConnected: false,
      isConnecting: false,
      messageText: '',
      messageType: this.defaultMessageType,
      connectionStatus: 'disconnected' // disconnected, connecting, connected, error
    }
  },
  computed: {
    containerStyle() {
      return {
        width: typeof this.width === 'number' ? `${this.width}px` : this.width,
        height: typeof this.height === 'number' ? `${this.height}px` : this.height
      }
    },
    statusClass() {
      return {
        'status-connected': this.connectionStatus === 'connected',
        'status-connecting': this.connectionStatus === 'connecting',
        'status-disconnected': this.connectionStatus === 'disconnected',
        'status-error': this.connectionStatus === 'error'
      }
    },
    statusText() {
      const statusMap = {
        disconnected: '未连接',
        connecting: '连接中',
        connected: '已连接',
        error: '连接错误'
      }
      return statusMap[this.connectionStatus] || '未知状态'
    }
  },
  mounted() {
    if (this.autoConnect) {
      this.connect()
    }
  },
  beforeDestroy() {
    this.disconnect()
  },
  methods: {
    async connect() {
      if (this.isConnecting || this.isConnected) return
      
      try {
        this.isConnecting = true
        this.connectionStatus = 'connecting'
        this.$emit('connecting')
        
        // 创建WebRTC连接
        const config = {
          sdpSemantics: 'unified-plan',
          iceServers: [{ urls: ['stun:stun.l.google.com:19302'] }]
        }
        
        this.pc = new RTCPeerConnection(config)
        
        // 处理接收到的音视频流
        this.pc.addEventListener('track', (evt) => {
          if (evt.track.kind === 'video') {
            this.$refs.digitalHumanVideo.srcObject = evt.streams[0]
          } else if (evt.track.kind === 'audio') {
            this.$refs.digitalHumanAudio.srcObject = evt.streams[0]
          }
        })
        
        // 监听连接状态变化
        this.pc.addEventListener('connectionstatechange', () => {
          console.log('WebRTC连接状态:', this.pc.connectionState)
          if (this.pc.connectionState === 'failed') {
            this.handleConnectionError('WebRTC连接失败')
          }
        })
        
        // 添加接收器
        this.pc.addTransceiver('video', { direction: 'recvonly' })
        this.pc.addTransceiver('audio', { direction: 'recvonly' })
        
        // 创建offer
        const offer = await this.pc.createOffer()
        await this.pc.setLocalDescription(offer)
        
        // 等待ICE收集完成
        await this.waitForIceGathering()
        
        // 发送offer到服务器
        const response = await fetch(`${this.serverUrl}/offer`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            sdp: this.pc.localDescription.sdp,
            type: this.pc.localDescription.type
          })
        })
        
        if (!response.ok) {
          throw new Error(`服务器响应错误: ${response.status}`)
        }
        
        const answer = await response.json()
        this.sessionId = answer.sessionid
        
        // 设置远程描述
        await this.pc.setRemoteDescription(answer)
        
        this.isConnected = true
        this.connectionStatus = 'connected'
        this.$emit('connected', { sessionId: this.sessionId })
        
        console.log('数字人连接成功，会话ID:', this.sessionId)
        
      } catch (error) {
        this.handleConnectionError(error.message)
      } finally {
        this.isConnecting = false
      }
    },
    
    disconnect() {
      if (this.pc) {
        this.pc.close()
        this.pc = null
      }
      
      this.isConnected = false
      this.isConnecting = false
      this.sessionId = 0
      this.connectionStatus = 'disconnected'
      
      // 清空视频流
      if (this.$refs.digitalHumanVideo) {
        this.$refs.digitalHumanVideo.srcObject = null
      }
      if (this.$refs.digitalHumanAudio) {
        this.$refs.digitalHumanAudio.srcObject = null
      }
      
      this.$emit('disconnected')
      console.log('数字人连接已断开')
    },
    
    async sendMessage() {
      if (!this.isConnected || !this.messageText.trim()) return
      
      const message = this.messageText.trim()
      
      try {
        const response = await fetch(`${this.serverUrl}/human`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            text: message,
            type: this.messageType,
            interrupt: true,
            sessionid: this.sessionId
          })
        })
        
        if (!response.ok) {
          throw new Error(`发送消息失败: ${response.status}`)
        }
        
        this.$emit('message-sent', {
          text: message,
          type: this.messageType
        })
        
        this.messageText = ''
        console.log('消息发送成功:', message)
        
      } catch (error) {
        this.$emit('error', error.message)
        console.error('发送消息失败:', error)
      }
    },
    
    waitForIceGathering() {
      return new Promise((resolve) => {
        if (this.pc.iceGatheringState === 'complete') {
          resolve()
        } else {
          const checkState = () => {
            if (this.pc.iceGatheringState === 'complete') {
              this.pc.removeEventListener('icegatheringstatechange', checkState)
              resolve()
            }
          }
          this.pc.addEventListener('icegatheringstatechange', checkState)
        }
      })
    },
    
    handleConnectionError(errorMessage) {
      this.connectionStatus = 'error'
      this.isConnecting = false
      this.isConnected = false
      this.$emit('error', errorMessage)
      console.error('连接错误:', errorMessage)
    },
    
    // 公共方法：发送指定文本
    sendText(text, type = 'chat') {
      if (!this.isConnected) {
        console.warn('数字人未连接，无法发送消息')
        return Promise.reject(new Error('数字人未连接'))
      }
      
      return fetch(`${this.serverUrl}/human`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: text,
          type: type,
          interrupt: true,
          sessionid: this.sessionId
        })
      }).then(response => {
        if (!response.ok) {
          throw new Error(`发送失败: ${response.status}`)
        }
        this.$emit('message-sent', { text, type })
        return response
      })
    }
  }
}
</script>

<style scoped>
.digital-human-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.digital-human-container {
  position: relative;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background-color: #000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.digital-human-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.status-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 12px;
  backdrop-filter: blur(4px);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #dc3545;
}

.status-connected .status-dot {
  background-color: #28a745;
}

.status-connecting .status-dot {
  background-color: #ffc107;
  animation: pulse 1.5s infinite;
}

.status-error .status-dot {
  background-color: #dc3545;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.controls-panel {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.control-buttons {
  display: flex;
  gap: 8px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.btn-send {
  background-color: #28a745;
  color: white;
  min-width: 60px;
}

.btn-send:hover:not(:disabled) {
  background-color: #218838;
}

.message-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-wrapper {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  min-height: 40px;
  font-family: inherit;
}

.message-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.mode-switch {
  display: flex;
  gap: 16px;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  cursor: pointer;
}

.radio-label input[type="radio"] {
  margin: 0;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .control-buttons {
    flex-direction: column;
  }

  .input-wrapper {
    flex-direction: column;
    align-items: stretch;
  }

  .mode-switch {
    justify-content: center;
  }
}
</style>
